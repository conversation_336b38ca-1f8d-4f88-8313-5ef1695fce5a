package com.ruoyi.business.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.business.service.IBusinessService;

/**
 * 业务控制器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/business")
public class BusinessController
{
    @Autowired
    private IBusinessService businessService;
    /**
     * 业务首页
     */
    @GetMapping()
    public String business()
    {
        return "business/index";
    }

    /**
     * 业务数据接口示例
     */
    @GetMapping("/data")
    @ResponseBody
    public AjaxResult getData()
    {
        String data = businessService.getBusinessData();
        return AjaxResult.success(data);
    }
}
